import { useEffect, useCallback, useRef, useState } from 'react'
import webSocketService from '../lib/websocket'
import useAuthStore from '../store/authStore'

/**
 * Enhanced WebSocket hook for slot selection workflow
 * Manages subscription lifecycle: subscribe when viewing slots, unsubscribe when slot locked/payment, re-subscribe when returning
 */
const usePersistentSlotUpdates = (shopId, serviceId, employeeId, date, onSlotUpdate, isOnSlotSelection = true) => {
  const { token } = useAuthStore()
  const [isConnected, setIsConnected] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const [currentTopic, setCurrentTopic] = useState(null)

  // Refs to track subscription state
  const subscriptionTopicRef = useRef(null)
  const isActiveRef = useRef(true) // Track if component is still active
  const currentDateRef = useRef(date) // Track current date immediately
  const isOnSlotSelectionRef = useRef(isOnSlotSelection) // Track if user is on slot selection screen

  // Update refs when props change
  useEffect(() => {
    isOnSlotSelectionRef.current = isOnSlotSelection
    currentDateRef.current = date
  }, [isOnSlotSelection, date])

  // Ensure component is marked as active when hook is being used
  useEffect(() => {
    isActiveRef.current = true
  }, [])

  // Stable callback reference - define this first
  const stableOnSlotUpdate = useCallback((message) => {
    if (isActiveRef.current && onSlotUpdate) {
      onSlotUpdate(message)
    }
  }, [onSlotUpdate])

  // Build topic based on current parameters
  const buildTopic = useCallback(() => {
    if (!shopId || !serviceId || !date) return null

    return employeeId
      ? `slots.${shopId}.${serviceId}.${employeeId}.${date}`
      : `slots.${shopId}.${serviceId}.${date}`
  }, [shopId, serviceId, employeeId, date])

  // Subscribe to topic
  const subscribeToTopic = useCallback(async (topic) => {
    if (!topic || !isActiveRef.current) return false

    try {

      // Ensure WebSocket is connected
      if (!webSocketService.isConnected()) {
        await webSocketService.connect(token, true)
      }

      // Subscribe to the topic
      webSocketService.subscribe(topic, stableOnSlotUpdate)

      subscriptionTopicRef.current = topic
      setCurrentTopic(topic)
      setIsSubscribed(true)

      return true
    } catch (error) {
      console.error('❌ Failed to subscribe to topic:', topic, error)
      setConnectionError(error.message)
      return false
    }
  }, [token, stableOnSlotUpdate])

  // Unsubscribe from current topic
  const unsubscribeFromCurrentTopic = useCallback(() => {
    const currentTopic = subscriptionTopicRef.current
    if (currentTopic) {
      webSocketService.unsubscribe(currentTopic)
      subscriptionTopicRef.current = null
      setCurrentTopic(null)
      setIsSubscribed(false)
    }
  }, [])

  // Main subscription management effect
  useEffect(() => {
      shopId, serviceId, employeeId, date,
      isOnSlotSelection: isOnSlotSelectionRef.current,
      isActive: isActiveRef.current
    })

    const targetTopic = buildTopic()
    const currentTopic = subscriptionTopicRef.current

    // Only subscribe if user is on slot selection screen and we have valid parameters
    if (isOnSlotSelectionRef.current && targetTopic && isActiveRef.current) {
      // If topic changed, unsubscribe from old and subscribe to new
      if (targetTopic !== currentTopic) {

        // Unsubscribe from old topic if exists
        if (currentTopic) {
          unsubscribeFromCurrentTopic()
        }

        // Subscribe to new topic
        subscribeToTopic(targetTopic)
      } else if (!currentTopic) {
        // No current subscription but we should have one
        subscribeToTopic(targetTopic)
      } else {
      }
    } else if (!isOnSlotSelectionRef.current && currentTopic) {
      // User is not on slot selection screen, unsubscribe
      unsubscribeFromCurrentTopic()
    } else if (!targetTopic) {
    }
  }, [shopId, serviceId, employeeId, date, isOnSlotSelection, buildTopic, subscribeToTopic, unsubscribeFromCurrentTopic])

  // Connection monitoring effect
  useEffect(() => {
    const checkConnection = () => {
      const connected = webSocketService.isConnected()
      setIsConnected(connected)

      if (!connected && isActiveRef.current) {
        setConnectionError('WebSocket disconnected')
      } else if (connected) {
        setConnectionError(null)
      }
    }

    // Initial check
    checkConnection()

    // Set up periodic monitoring
    const interval = setInterval(checkConnection, 3000)

    return () => clearInterval(interval)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isActiveRef.current = false
      unsubscribeFromCurrentTopic()
    }
  }, [unsubscribeFromCurrentTopic])

  return {
    isConnected,
    isSubscribed,
    connectionError,
    currentTopic,
    unsubscribe: unsubscribeFromCurrentTopic
  }
}

export default usePersistentSlotUpdates
